

-- 1. Section Table (Parent table)
CREATE TABLE section (
  section_id VARCHAR(10) PRIMARY KEY,
  section_name VARCHAR(50)
);

-- 2. Student Table (References Section)
CREATE TABLE student (
  id INT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(30),
  class VARCHAR(30),
  uid VARCHAR(20),
  mobile_no BIGINT,
  section_id VARCHAR(10),
  FOREIGN KEY (section_id) REFERENCES section(section_id)
);

-- 3. Mark Table (References Student)
CREATE TABLE mark (
  mark_id INT PRIMARY KEY AUTO_INCREMENT,  -- unique mark record ID
  student_id INT,
  subject VARCHAR(50),
  marks INT,
  FOREIGN KEY (student_id) REFERENCES student(id)
);

-- Insert Sample Data

-- Insert sections
INSERT INTO section (section_id, section_name) VALUES
('A', 'Section A'),
('B', 'Section B'),
('C', 'Section C');

-- Insert students
INSERT INTO student (id, name, class, uid, mobile_no, section_id) VALUES
  (1, 'Vishal', 'MCA', '25MCA20127', 9112564731, 'B'),
  (2, 'Ambuj', 'MC<PERSON>', '25MCA20126', 9112567839, 'B'),
  (3, 'Shubham', 'MCI', '25MCA20124', 9112345789, 'A');

-- Insert marks
INSERT INTO mark (student_id, subject, marks) VALUES
  (1, 'Math', 85),
  (1, 'Science', 90),
  (2, 'Math', 78),
  (3, 'Science', 88);

-- Create Indexes for better performance
CREATE INDEX idx_student_section ON student(section_id);
CREATE INDEX idx_mark_student ON mark(student_id);
CREATE INDEX idx_student_uid ON student(uid);

-- Sample Queries to verify the relationships

-- Query 1: Show all students with their section details
SELECT s.id, s.name, s.class, s.uid, s.mobile_no, sec.section_name
FROM student s
JOIN section sec ON s.section_id = sec.section_id;

-- Query 2: Show all marks with student and section information
SELECT m.mark_id, m.subject, m.marks,
       s.name as student_name, s.uid, sec.section_name
FROM mark m
JOIN student s ON m.student_id = s.id
JOIN section sec ON s.section_id = sec.section_id;

-- Query 3: Calculate average marks per student
SELECT s.name, s.uid,
       ROUND(AVG(m.marks), 2) as average_marks,
       COUNT(m.marks) as total_subjects
FROM student s
LEFT JOIN mark m ON s.id = m.student_id
GROUP BY s.id, s.name, s.uid;

-- Query 4: Show students by section
SELECT sec.section_name, COUNT(s.id) as student_count
FROM section sec
LEFT JOIN student s ON sec.section_id = s.section_id
GROUP BY sec.section_id, sec.section_name;