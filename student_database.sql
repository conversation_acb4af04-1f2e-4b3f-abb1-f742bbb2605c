

-- 1. Section Table (Parent table)
CREATE TABLE section (
    section_id INT PRIMARY KEY AUTO_INCREMENT,
    section_name VARCHAR(50) NOT NULL UNIQUE,
    class_teacher VARCHAR(100),
    room_number VARCHAR(10),
    created_date DATE DEFAULT (CURRENT_DATE)
);

-- 2. Student Table (References Section)
CREATE TABLE student (
    id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    class VARCHAR(20) NOT NULL,
    uid VARCHAR(20) NOT NULL UNIQUE,
    mobile_no BIGINT,
    section_id VARCHAR(10) NOT NULL,

    -- Foreign Key Constraint
    CONSTRAINT fk_student_section
        FOREIGN KEY (section_id)
        REFERENCES section(section_name)
        ON DELETE RESTRICT
        ON UPDATE CASCADE
);

-- 3. Marks Table (References Student)
CREATE TABLE marks (
    mark_id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_name VARCHAR(100) NOT NULL,
    exam_type ENUM('Quiz', 'Midterm', 'Final', 'Assignment', 'Project') NOT NULL,
    marks_obtained DECIMAL(5,2) NOT NULL,
    total_marks DECIMAL(5,2) NOT NULL,
    exam_date DATE NOT NULL,
    remarks TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign Key Constraint
    CONSTRAINT fk_marks_student
        FOREIGN KEY (student_id)
        REFERENCES student(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Check Constraints
    CONSTRAINT chk_marks_obtained
        CHECK (marks_obtained >= 0 AND marks_obtained <= total_marks),
    CONSTRAINT chk_total_marks
        CHECK (total_marks > 0)
);

-- Create Indexes for better performance
CREATE INDEX idx_student_section ON student(section_id);
CREATE INDEX idx_marks_student ON marks(student_id);
CREATE INDEX idx_marks_subject ON marks(subject_name);
CREATE INDEX idx_student_uid ON student(uid);

-- Insert Sample Data

-- Sample Sections
INSERT INTO section (section_name, class_teacher, room_number) VALUES
('MCA-A', 'Dr. Rajesh Kumar', 'Room-101'),
('MCA-B', 'Prof. Priya Sharma', 'Room-102'),
('MCA-C', 'Dr. Amit Singh', 'Room-103');

-- Sample Students
INSERT INTO student (student_name, roll_number, email, phone, date_of_birth, address, section_id) VALUES
('Rahul Verma', 'MCA001', '<EMAIL>', '9876543210', '2000-05-15', '123 Main St, Chandigarh', 1),
('Priya Gupta', 'MCA002', '<EMAIL>', '9876543211', '1999-08-22', '456 Park Ave, Chandigarh', 1),
('Amit Sharma', 'MCA003', '<EMAIL>', '9876543212', '2000-12-10', '789 Oak St, Chandigarh', 2),
('Neha Singh', 'MCA004', '<EMAIL>', '9876543213', '1999-03-18', '321 Pine St, Chandigarh', 2),
('Vikash Kumar', 'MCA005', '<EMAIL>', '9876543214', '2000-07-25', '654 Elm St, Chandigarh', 3);

-- Sample Marks
INSERT INTO marks (student_id, subject_name, exam_type, marks_obtained, total_marks, exam_date, remarks) VALUES
(1, 'Database Management Systems', 'Midterm', 85.50, 100.00, '2024-10-15', 'Good performance'),
(1, 'Data Structures', 'Quiz', 18.00, 20.00, '2024-10-10', 'Excellent'),
(2, 'Database Management Systems', 'Midterm', 78.00, 100.00, '2024-10-15', 'Above average'),
(2, 'Data Structures', 'Quiz', 16.50, 20.00, '2024-10-10', 'Good'),
(3, 'Computer Networks', 'Assignment', 92.00, 100.00, '2024-10-12', 'Outstanding work'),
(4, 'Software Engineering', 'Project', 88.75, 100.00, '2024-10-20', 'Creative solution'),
(5, 'Operating Systems', 'Final', 76.25, 100.00, '2024-10-25', 'Satisfactory');

