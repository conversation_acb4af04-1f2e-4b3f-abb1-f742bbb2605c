import sqlite3
import os

# Create database connection
conn = sqlite3.connect('student_database.db')
cursor = conn.cursor()

print("🗄️  Creating Student Database...")
print("=" * 50)

# Read and execute the SQL file
try:
    with open('student_database.sql', 'r') as file:
        sql_content = file.read()
    
    # Split SQL commands and execute them
    sql_commands = sql_content.split(';')

    for command in sql_commands:
        command = command.strip()
        if command and not command.startswith('--') and 'SELECT' not in command.upper():
            print(f"Executing: {command[:50]}...")
            cursor.execute(command)
    
    conn.commit()
    print("✅ Tables created and data inserted successfully!")
    print()
    
    # Now run the queries to show output
    print("📊 QUERY RESULTS:")
    print("=" * 50)
    
    # Query 1: Show all students with their section details
    print("\n🔍 Query 1: Students with Section Details")
    print("-" * 45)
    cursor.execute("""
        SELECT s.id, s.name, s.class, s.uid, s.mobile_no, sec.section_name 
        FROM student s 
        JOIN section sec ON s.section_id = sec.section_id
    """)
    
    results = cursor.fetchall()
    print(f"{'ID':<3} {'Name':<8} {'Class':<5} {'UID':<12} {'Mobile':<11} {'Section':<10}")
    print("-" * 60)
    for row in results:
        print(f"{row[0]:<3} {row[1]:<8} {row[2]:<5} {row[3]:<12} {row[4]:<11} {row[5]:<10}")
    
    # Query 2: Show all marks with student and section information
    print("\n🔍 Query 2: Marks with Student and Section Info")
    print("-" * 50)
    cursor.execute("""
        SELECT m.mark_id, m.subject, m.marks, 
               s.name as student_name, s.uid, sec.section_name
        FROM mark m
        JOIN student s ON m.student_id = s.id
        JOIN section sec ON s.section_id = sec.section_id
    """)
    
    results = cursor.fetchall()
    print(f"{'ID':<3} {'Subject':<8} {'Marks':<6} {'Student':<8} {'UID':<12} {'Section':<10}")
    print("-" * 60)
    for row in results:
        print(f"{row[0]:<3} {row[1]:<8} {row[2]:<6} {row[3]:<8} {row[4]:<12} {row[5]:<10}")
    
    # Query 3: Calculate average marks per student
    print("\n🔍 Query 3: Average Marks per Student")
    print("-" * 40)
    cursor.execute("""
        SELECT s.name, s.uid, 
               ROUND(AVG(m.marks), 2) as average_marks,
               COUNT(m.marks) as total_subjects
        FROM student s
        LEFT JOIN mark m ON s.id = m.student_id
        GROUP BY s.id, s.name, s.uid
    """)
    
    results = cursor.fetchall()
    print(f"{'Name':<8} {'UID':<12} {'Avg Marks':<10} {'Subjects':<8}")
    print("-" * 45)
    for row in results:
        avg_marks = row[2] if row[2] is not None else 0
        subjects = row[3] if row[3] is not None else 0
        print(f"{row[0]:<8} {row[1]:<12} {avg_marks:<10} {subjects:<8}")
    
    # Query 4: Show students by section
    print("\n🔍 Query 4: Students Count by Section")
    print("-" * 35)
    cursor.execute("""
        SELECT sec.section_name, COUNT(s.id) as student_count
        FROM section sec
        LEFT JOIN student s ON sec.section_id = s.section_id
        GROUP BY sec.section_id, sec.section_name
    """)
    
    results = cursor.fetchall()
    print(f"{'Section':<12} {'Count':<6}")
    print("-" * 20)
    for row in results:
        print(f"{row[0]:<12} {row[1]:<6}")
    
    print("\n✅ Database operations completed successfully!")
    print("🎯 All relationships are working properly!")
    
except Exception as e:
    print(f"❌ Error: {e}")

finally:
    conn.close()
