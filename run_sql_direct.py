import sqlite3

# Create database connection
conn = sqlite3.connect('student_database.db')
cursor = conn.cursor()

print("🗄️  Creating Student Database...")
print("=" * 50)

try:
    # Create tables directly
    print("Creating section table...")
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS section (
          section_id VARCHAR(10) PRIMARY KEY,
          section_name VARCHAR(50)
        )
    """)
    
    print("Creating student table...")
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS student (
          id INT PRIMARY KEY,
          name VARCHAR(30),
          class VARCHAR(30),
          uid VARCHAR(20),
          mobile_no BIGINT,
          section_id VARCHAR(10),
          FOREIGN KEY (section_id) REFERENCES section(section_id)
        )
    """)
    
    print("Creating mark table...")
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS mark (
          mark_id INTEGER PRIMARY KEY AUTOINCREMENT,
          student_id INT,
          subject VARCHAR(50),
          marks INT,
          <PERSON>OREI<PERSON><PERSON> KEY (student_id) REFERENCES student(id)
        )
    """)
    
    # Insert data
    print("Inserting sections...")
    cursor.execute("DELETE FROM section")  # Clear existing data
    cursor.execute("INSERT INTO section (section_id, section_name) VALUES ('A', 'Section A')")
    cursor.execute("INSERT INTO section (section_id, section_name) VALUES ('B', 'Section B')")
    cursor.execute("INSERT INTO section (section_id, section_name) VALUES ('C', 'Section C')")
    
    print("Inserting students...")
    cursor.execute("DELETE FROM student")  # Clear existing data
    cursor.execute("INSERT INTO student (id, name, class, uid, mobile_no, section_id) VALUES (1, 'Vishal', 'MCA', '25MCA20127', 9112564731, 'B')")
    cursor.execute("INSERT INTO student (id, name, class, uid, mobile_no, section_id) VALUES (2, 'Ambuj', 'MCI', '25MCA20126', 9112567839, 'B')")
    cursor.execute("INSERT INTO student (id, name, class, uid, mobile_no, section_id) VALUES (3, 'Shubham', 'MCI', '25MCA20124', 9112345789, 'A')")
    
    print("Inserting marks...")
    cursor.execute("DELETE FROM mark")  # Clear existing data
    cursor.execute("INSERT INTO mark (student_id, subject, marks) VALUES (1, 'Math', 85)")
    cursor.execute("INSERT INTO mark (student_id, subject, marks) VALUES (1, 'Science', 90)")
    cursor.execute("INSERT INTO mark (student_id, subject, marks) VALUES (2, 'Math', 78)")
    cursor.execute("INSERT INTO mark (student_id, subject, marks) VALUES (3, 'Science', 88)")
    
    conn.commit()
    print("✅ Tables created and data inserted successfully!")
    print()
    
    # Now run the queries to show output
    print("📊 QUERY RESULTS:")
    print("=" * 50)
    
    # Query 1: Show all students with their section details
    print("\n🔍 Query 1: Students with Section Details")
    print("-" * 45)
    cursor.execute("""
        SELECT s.id, s.name, s.class, s.uid, s.mobile_no, sec.section_name 
        FROM student s 
        JOIN section sec ON s.section_id = sec.section_id
    """)
    
    results = cursor.fetchall()
    print(f"{'ID':<3} {'Name':<8} {'Class':<5} {'UID':<12} {'Mobile':<11} {'Section':<10}")
    print("-" * 60)
    for row in results:
        print(f"{row[0]:<3} {row[1]:<8} {row[2]:<5} {row[3]:<12} {row[4]:<11} {row[5]:<10}")
    
    # Query 2: Show all marks with student and section information
    print("\n🔍 Query 2: Marks with Student and Section Info")
    print("-" * 50)
    cursor.execute("""
        SELECT m.mark_id, m.subject, m.marks, 
               s.name as student_name, s.uid, sec.section_name
        FROM mark m
        JOIN student s ON m.student_id = s.id
        JOIN section sec ON s.section_id = sec.section_id
    """)
    
    results = cursor.fetchall()
    print(f"{'ID':<3} {'Subject':<8} {'Marks':<6} {'Student':<8} {'UID':<12} {'Section':<10}")
    print("-" * 60)
    for row in results:
        print(f"{row[0]:<3} {row[1]:<8} {row[2]:<6} {row[3]:<8} {row[4]:<12} {row[5]:<10}")
    
    # Query 3: Calculate average marks per student
    print("\n🔍 Query 3: Average Marks per Student")
    print("-" * 40)
    cursor.execute("""
        SELECT s.name, s.uid, 
               ROUND(AVG(CAST(m.marks AS FLOAT)), 2) as average_marks,
               COUNT(m.marks) as total_subjects
        FROM student s
        LEFT JOIN mark m ON s.id = m.student_id
        GROUP BY s.id, s.name, s.uid
    """)
    
    results = cursor.fetchall()
    print(f"{'Name':<8} {'UID':<12} {'Avg Marks':<10} {'Subjects':<8}")
    print("-" * 45)
    for row in results:
        avg_marks = row[2] if row[2] is not None else 0
        subjects = row[3] if row[3] is not None else 0
        print(f"{row[0]:<8} {row[1]:<12} {avg_marks:<10} {subjects:<8}")
    
    # Query 4: Show students by section
    print("\n🔍 Query 4: Students Count by Section")
    print("-" * 35)
    cursor.execute("""
        SELECT sec.section_name, COUNT(s.id) as student_count
        FROM section sec
        LEFT JOIN student s ON sec.section_id = s.section_id
        GROUP BY sec.section_id, sec.section_name
        ORDER BY sec.section_id
    """)
    
    results = cursor.fetchall()
    print(f"{'Section':<12} {'Count':<6}")
    print("-" * 20)
    for row in results:
        print(f"{row[0]:<12} {row[1]:<6}")
    
    print("\n✅ Database operations completed successfully!")
    print("🎯 All relationships are working properly!")
    print(f"📁 Database file created: student_database.db")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

finally:
    conn.close()
